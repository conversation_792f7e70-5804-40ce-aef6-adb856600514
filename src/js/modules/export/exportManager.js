/**
 * @file EXPORT MANAGER MODULE
 *
 * @version 1.0.0
 * <AUTHOR>
 * @contributors
 * @license MIT
 *
 * @description
 * Export management module for The Great Calculator.
 * Handles exporting calculator data to various formats including CSV, PDF, JSON, and TXT.
 * Provides comprehensive export options, metadata inclusion, and file download functionality.
 *
 * Features:
 * - Multi-format export support (CSV, PDF, JSON, TXT)
 * - Configurable export options and metadata
 * - Automatic file download functionality
 * - Custom formula export capabilities
 * - Timestamp and formatting options
 * - Error handling and validation
 * - Export statistics and monitoring
 *
 * @requires Web APIs: Blob, URL, DOM manipulation
 */

// ------------ TYPE AND JSDOC DEFINITIONS

/**
 * @typedef {Object} ExportOptions
 * @property {boolean} [includeTimestamp=true] - Whether to include timestamps
 * @property {boolean} [includeMetadata=true] - Whether to include metadata
 * @property {string} [dateFormat='YYYY-MM-DD HH:mm:ss'] - Date format string
 * @property {string} [csvDelimiter=','] - CSV delimiter character
 * @property {string} [pdfPageSize='A4'] - PDF page size
 * @property {string} [pdfOrientation='portrait'] - PDF orientation
 */

/**
 * @typedef {Object} CalculationEntry
 * @property {string} expression - Mathematical expression
 * @property {string} result - Calculation result
 * @property {string} [timestamp] - When calculation was performed
 * @property {number} [index] - Position in history
 */

/**
 * @typedef {Object} ParsedCalculation
 * @property {string} expression - The mathematical expression
 * @property {string} result - The calculation result
 */

/**
 * @typedef {Object} ExportMetadata
 * @property {string} exportDate - When export was generated
 * @property {number} totalCalculations - Number of calculations exported
 * @property {number} [totalFormulas] - Number of formulas exported
 * @property {string} format - Export format used
 * @property {string} [version] - Application version
 * @property {string} [type] - Type of export (history, formulas)
 */

/**
 * @typedef {Object} FormulaEntry
 * @property {string} name - Formula name
 * @property {string} formula - Formula expression
 * @property {string} description - Formula description
 * @property {string} created - Creation date
 * @property {number} useCount - Number of times used
 */

/**
 * @typedef {Object} ExportStatistics
 * @property {string[]} supportedFormats - List of supported export formats
 * @property {ExportOptions} defaultOptions - Default export options
 */

// ------------ EXPORT MANAGER CLASS

/**
 * Export Manager Class
 *
 * Provides comprehensive export functionality for calculator data
 * with support for multiple formats and configurable options.
 *
 * @class ExportManager
 * @example
 * const exportManager = new ExportManager();
 *
 * // Export calculation history to CSV
 * await exportManager.exportHistory(historyArray, 'csv', {
 *   includeTimestamp: true,
 *   includeMetadata: true
 * });
 *
 * // Export custom formulas to JSON
 * await exportManager.exportFormulas(formulasArray, 'json');
 */
class ExportManager {
    /**
     * Create export manager instance
     *
     * Initializes the export manager with supported formats and default options.
     *
     * @constructor
     * @example
     * const exportManager = new ExportManager();
     */
    constructor() {
        /** @type {string[]} List of supported export formats */
        this.supportedFormats = ['csv', 'pdf', 'json', 'txt'];

        /** @type {ExportOptions} Default export configuration options */
        this.defaultOptions = {
            includeTimestamp: true,
            includeMetadata: true,
            dateFormat: 'YYYY-MM-DD HH:mm:ss',
            csvDelimiter: ',',
            pdfPageSize: 'A4',
            pdfOrientation: 'portrait'
        };
    }

    // ------------ MAIN EXPORT METHODS

    /**
     * Export calculation history to specified format
     *
     * Main export method that delegates to format-specific exporters
     * with comprehensive error handling and validation.
     *
     * @async
     * @method exportHistory
     * @param {string[]} history - Array of calculation history strings
     * @param {string} [format='csv'] - Export format: 'csv', 'pdf', 'json', 'txt'
     * @param {ExportOptions} [options={}] - Export configuration options
     * @returns {Promise<void>} Resolves when export is complete
     *
     * @throws {Error} When format is unsupported or history is empty
     *
     * @example
     * const history = ['2 + 2 = 4', '10 * 5 = 50', '100 / 4 = 25'];
     *
     * // Export to CSV with default options
     * await exportManager.exportHistory(history, 'csv');
     *
     * // Export to PDF with custom options
     * await exportManager.exportHistory(history, 'pdf', {
     *   includeMetadata: true,
     *   pdfPageSize: 'Letter'
     * });
     */
    async exportHistory(history, format = 'csv', options = {}) {
        /** @type {ExportOptions} */
        const exportOptions = { ...this.defaultOptions, ...options };

        if (!this.supportedFormats.includes(format)) {
            throw new Error(`Unsupported export format: ${format}`);
        }

        if (!history || history.length === 0) {
            throw new Error('No history data to export');
        }

        try {
            switch (format) {
                case 'csv':
                    return await this.exportToCSV(history, exportOptions);
                case 'pdf':
                    return await this.exportToPDF(history, exportOptions);
                case 'json':
                    return await this.exportToJSON(history, exportOptions);
                case 'txt':
                    return await this.exportToTXT(history, exportOptions);
                default:
                    throw new Error(`Export format ${format} not implemented`);
            }
        } catch (error) {
            console.error('Export failed:', error);
            throw new Error(`Export failed: ${error.message}`);
        }
    }

    // ------------ FORMAT-SPECIFIC EXPORT METHODS

    /**
     * Export to CSV format
     *
     * Generates a CSV file with calculation history including optional
     * metadata headers and timestamps.
     *
     * @async
     * @method exportToCSV
     * @param {string[]} history - Array of calculation history strings
     * @param {ExportOptions} options - Export configuration options
     * @returns {Promise<void>} Resolves when CSV export is complete
     *
     * @example
     * const history = ['2 + 2 = 4', '10 * 5 = 50'];
     * const options = { includeTimestamp: true, csvDelimiter: ';' };
     * await exportManager.exportToCSV(history, options);
     */
    async exportToCSV(history, options) {
        const { includeTimestamp, includeMetadata, csvDelimiter } = options;

        /** @type {string} */
        let csvContent = '';

        // Add metadata header
        if (includeMetadata) {
            csvContent += `# Calculator History Export\n`;
            csvContent += `# Generated: ${this.getCurrentTimestamp()}\n`;
            csvContent += `# Total Calculations: ${history.length}\n`;
            csvContent += `#\n`;
        }

        // Add CSV headers
        /** @type {string[]} */
        const headers = ['Index', 'Calculation', 'Result'];
        if (includeTimestamp) {
            headers.push('Timestamp');
        }
        csvContent += `${headers.join(csvDelimiter)  }\n`;

        // Add data rows
        history.forEach((calculation, index) => {
            /** @type {ParsedCalculation} */
            const parts = this.parseCalculation(calculation);
            /** @type {string[]} */
            const row = [
                (index + 1).toString(),
                `"${parts.expression}"`,
                `"${parts.result}"`
            ];

            if (includeTimestamp) {
                row.push(`"${this.getCurrentTimestamp()}"`);
            }

            csvContent += `${row.join(csvDelimiter)  }\n`;
        });

        // Download CSV file
        this.downloadFile(csvContent, 'calculator-history.csv', 'text/csv');
    }

    /**
     * Export to PDF format
     *
     * Generates a PDF export by creating a printable HTML version and
     * opening it in a new window for printing. In production, consider
     * using libraries like jsPDF or Puppeteer for direct PDF generation.
     *
     * @async
     * @method exportToPDF
     * @param {string[]} history - Array of calculation history strings
     * @param {ExportOptions} options - Export configuration options
     * @returns {Promise<void>} Resolves when PDF export is initiated
     *
     * @example
     * const history = ['2 + 2 = 4', '10 * 5 = 50'];
     * await exportManager.exportToPDF(history, { includeMetadata: true });
     */
    async exportToPDF(history, options) {
        // For PDF generation, we'll create a printable HTML version
        // In a real implementation, you might use libraries like jsPDF or Puppeteer

        /** @type {string} */
        const htmlContent = this.generatePDFHTML(history, options);

        // Create a new window for printing
        /** @type {Window|null} */
        const printWindow = window.open('', '_blank');
        if (printWindow) {
            printWindow.document.write(htmlContent);
            printWindow.document.close();

            // Trigger print dialog
            printWindow.onload = () => {
                printWindow.print();
                // Close window after printing (optional)
                setTimeout(() => printWindow.close(), 1000);
            };
        }
    }

    /**
     * Generate HTML content for PDF export
     *
     * Creates a formatted HTML document with embedded CSS for printing
     * calculation history in a professional report format.
     *
     * @method generatePDFHTML
     * @param {string[]} history - Array of calculation history strings
     * @param {ExportOptions} options - Export configuration options
     * @returns {string} Complete HTML document string
     *
     * @example
     * const html = exportManager.generatePDFHTML(history, { includeMetadata: true });
     * console.log(html); // Complete HTML document
     */
    generatePDFHTML(history, options) {
        const { _includeTimestamp, includeMetadata } = options;

        /** @type {string} */
        let html = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Calculator History</title>
            <style>
                body {
                    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
                    margin: 40px;
                    color: #333;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 2px solid #007AFF;
                    padding-bottom: 20px;
                }
                .metadata {
                    background: #f8f9fa;
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 30px;
                }
                .calculation {
                    display: flex;
                    justify-content: space-between;
                    padding: 10px 0;
                    border-bottom: 1px solid #eee;
                    font-family: 'SF Mono', Monaco, monospace;
                }
                .calculation:last-child {
                    border-bottom: none;
                }
                .expression {
                    font-weight: 500;
                }
                .result {
                    color: #007AFF;
                    font-weight: 600;
                }
                @media print {
                    body { margin: 20px; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Calculator History Report</h1>
                <p>Generated on ${this.getCurrentTimestamp()}</p>
            </div>
        `;

        if (includeMetadata) {
            html += `
            <div class="metadata">
                <h3>Report Summary</h3>
                <p><strong>Total Calculations:</strong> ${history.length}</p>
                <p><strong>Export Date:</strong> ${this.getCurrentTimestamp()}</p>
                <p><strong>Format:</strong> PDF Report</p>
            </div>
            `;
        }

        html += `<div class="calculations">`;

        history.forEach((calculation, index) => {
            /** @type {ParsedCalculation} */
            const parts = this.parseCalculation(calculation);
            html += `
            <div class="calculation">
                <span class="index">${index + 1}.</span>
                <span class="expression">${parts.expression}</span>
                <span class="result">${parts.result}</span>
            </div>
            `;
        });

        html += `
            </div>
            <div class="footer" style="margin-top: 40px; text-align: center; color: #666;">
                <p>Generated by The Great Calculator</p>
            </div>
        </body>
        </html>
        `;

        return html;
    }

    /**
     * Export to JSON format
     *
     * Generates a structured JSON file with calculation history and
     * optional metadata in a machine-readable format.
     *
     * @async
     * @method exportToJSON
     * @param {string[]} history - Array of calculation history strings
     * @param {ExportOptions} options - Export configuration options
     * @returns {Promise<void>} Resolves when JSON export is complete
     *
     * @example
     * const history = ['2 + 2 = 4', '10 * 5 = 50'];
     * await exportManager.exportToJSON(history, { includeMetadata: true });
     */
    async exportToJSON(history, options) {
        const { includeTimestamp, includeMetadata } = options;

        /** @type {Object} */
        const exportData = {
            metadata: includeMetadata ? {
                exportDate: this.getCurrentTimestamp(),
                totalCalculations: history.length,
                format: 'json',
                version: '2.0.0'
            } : undefined,
            calculations: history.map((calculation, index) => {
                /** @type {ParsedCalculation} */
                const parts = this.parseCalculation(calculation);
                /** @type {CalculationEntry} */
                const item = {
                    index: index + 1,
                    expression: parts.expression,
                    result: parts.result
                };

                if (includeTimestamp) {
                    item.timestamp = this.getCurrentTimestamp();
                }

                return item;
            })
        };

        /** @type {string} */
        const jsonContent = JSON.stringify(exportData, null, 2);
        this.downloadFile(jsonContent, 'calculator-history.json', 'application/json');
    }

    /**
     * Export to plain text format
     *
     * Generates a simple, human-readable text file with calculation
     * history and optional metadata headers.
     *
     * @async
     * @method exportToTXT
     * @param {string[]} history - Array of calculation history strings
     * @param {ExportOptions} options - Export configuration options
     * @returns {Promise<void>} Resolves when TXT export is complete
     *
     * @example
     * const history = ['2 + 2 = 4', '10 * 5 = 50'];
     * await exportManager.exportToTXT(history, { includeMetadata: true });
     */
    async exportToTXT(history, options) {
        const { _includeTimestamp, includeMetadata } = options;

        /** @type {string} */
        let content = '';

        if (includeMetadata) {
            content += 'Calculator History Export\n';
            content += '========================\n\n';
            content += `Generated: ${this.getCurrentTimestamp()}\n`;
            content += `Total Calculations: ${history.length}\n\n`;
        }

        history.forEach((calculation, index) => {
            /** @type {ParsedCalculation} */
            const parts = this.parseCalculation(calculation);
            content += `${index + 1}. ${parts.expression} = ${parts.result}\n`;
        });

        if (includeMetadata) {
            content += '\n---\n';
            content += 'Generated by The Great Calculator\n';
        }

        this.downloadFile(content, 'calculator-history.txt', 'text/plain');
    }

    // ------------ UTILITY METHODS

    /**
     * Parse calculation string into expression and result
     *
     * Splits a calculation string (e.g., "2 + 2 = 4") into separate
     * expression and result components for structured export.
     *
     * @method parseCalculation
     * @param {string} calculation - Calculation string to parse
     * @returns {ParsedCalculation} Object with expression and result
     *
     * @example
     * const parsed = exportManager.parseCalculation('2 + 2 = 4');
     * console.log(parsed.expression); // "2 + 2"
     * console.log(parsed.result); // "4"
     */
    parseCalculation(calculation) {
        /** @type {string[]} */
        const parts = calculation.split(' = ');
        return {
            expression: parts[0] || calculation,
            result: parts[1] || 'N/A'
        };
    }

    /**
     * Get current timestamp in specified format
     *
     * Generates a formatted timestamp string for use in export metadata.
     * Uses simple date formatting - consider using date-fns in production.
     *
     * @method getCurrentTimestamp
     * @param {string} [format='YYYY-MM-DD HH:mm:ss'] - Date format (currently ignored)
     * @returns {string} Formatted timestamp string
     *
     * @example
     * const timestamp = exportManager.getCurrentTimestamp();
     * console.log(timestamp); // "2024-01-15 14:30:25"
     */
    getCurrentTimestamp(_format = 'YYYY-MM-DD HH:mm:ss') {
        /** @type {Date} */
        const now = new Date();

        // Simple date formatting (in production, consider using a library like date-fns)
        /** @type {number} */
        const year = now.getFullYear();
        /** @type {string} */
        const month = String(now.getMonth() + 1).padStart(2, '0');
        /** @type {string} */
        const day = String(now.getDate()).padStart(2, '0');
        /** @type {string} */
        const hours = String(now.getHours()).padStart(2, '0');
        /** @type {string} */
        const minutes = String(now.getMinutes()).padStart(2, '0');
        /** @type {string} */
        const seconds = String(now.getSeconds()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }

    /**
     * Download file with specified content
     *
     * Creates a downloadable file using the Blob API and triggers
     * an automatic download in the user's browser.
     *
     * @method downloadFile
     * @param {string} content - File content to download
     * @param {string} filename - Name for the downloaded file
     * @param {string} mimeType - MIME type for the file
     * @returns {void}
     *
     * @example
     * exportManager.downloadFile('Hello, World!', 'test.txt', 'text/plain');
     */
    downloadFile(content, filename, mimeType) {
        /** @type {Blob} */
        const blob = new Blob([content], { type: mimeType });
        /** @type {string} */
        const url = URL.createObjectURL(blob);

        /** @type {HTMLAnchorElement} */
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up the URL object
        setTimeout(() => URL.revokeObjectURL(url), 100);
    }

    // ------------ FORMULA EXPORT METHODS

    /**
     * Export custom formulas
     *
     * Exports user-defined formulas to specified format with metadata
     * including usage statistics and creation dates.
     *
     * @async
     * @method exportFormulas
     * @param {FormulaEntry[]} formulas - Array of formula objects
     * @param {string} [format='json'] - Export format: 'json' or 'csv'
     * @param {ExportOptions} [options={}] - Export configuration options
     * @returns {Promise<void>} Resolves when formula export is complete
     *
     * @throws {Error} When no formulas provided or format unsupported
     *
     * @example
     * const formulas = [
     *   {
     *     name: 'Quadratic',
     *     formula: 'a*x^2 + b*x + c',
     *     description: 'Quadratic equation',
     *     created: '2024-01-01',
     *     useCount: 5
     *   }
     * ];
     * await exportManager.exportFormulas(formulas, 'json');
     */
    async exportFormulas(formulas, format = 'json', _options = {}) {
        if (!formulas || formulas.length === 0) {
            throw new Error('No formulas to export');
        }

        /** @type {Object} */
        const exportData = {
            metadata: {
                exportDate: this.getCurrentTimestamp(),
                totalFormulas: formulas.length,
                format,
                type: 'formulas'
            },
            formulas: formulas.map(formula => ({
                name: formula.name,
                formula: formula.formula,
                description: formula.description,
                created: formula.created,
                useCount: formula.useCount
            }))
        };

        if (format === 'json') {
            /** @type {string} */
            const content = JSON.stringify(exportData, null, 2);
            this.downloadFile(content, 'calculator-formulas.json', 'application/json');
        } else if (format === 'csv') {
            /** @type {string} */
            let csvContent = 'Name,Formula,Description,Created,Use Count\n';
            formulas.forEach(formula => {
                csvContent += `"${formula.name}","${formula.formula}","${formula.description}","${formula.created}",${formula.useCount}\n`;
            });
            this.downloadFile(csvContent, 'calculator-formulas.csv', 'text/csv');
        }
    }

    // ------------ STATISTICS METHODS

    /**
     * Get export statistics
     *
     * Returns information about supported export formats and
     * default configuration options for the export manager.
     *
     * @method getStatistics
     * @returns {ExportStatistics} Export manager statistics
     *
     * @example
     * const stats = exportManager.getStatistics();
     * console.log('Supported formats:', stats.supportedFormats);
     * console.log('Default options:', stats.defaultOptions);
     */
    getStatistics() {
        return {
            supportedFormats: this.supportedFormats,
            defaultOptions: this.defaultOptions
        };
    }
}

// ------------ MODULE EXPORTS AND GLOBAL REGISTRATION

/**
 * Export for ES6 module systems (modern bundlers, native ES modules)
 * Enables tree-shaking and modern import/export syntax.
 *
 * @default ExportManager
 */
export default ExportManager;
